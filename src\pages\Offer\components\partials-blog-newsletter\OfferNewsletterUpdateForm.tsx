import HtmlEditor from "@/components/HtmlEditor";
import { LS_TOKEN_NAME } from "@/constants";
import { deleteFile } from "@/services/app/File/file";
import { updateOrCreateOfferBlogByOfferNo } from "@/services/app/Offer/offer-blog";
import Util, { sn } from "@/util";
import { ProColumns, ProForm, ProFormInstance, ProFormText, ProFormUploadDragger, EditableProTable } from "@ant-design/pro-components";
import { Button, message, Modal, Popconfirm, Space } from "antd";
import { RcFile } from "antd/es/upload";
import { useCallback, useRef, useState } from "react";

type FormValueType = API.OfferBlog;

export type OfferNewsletterUpdateFormProps = {
  offer_no: string;
  onSubmit?: (formData: FormValueType) => void;
};

const OfferNewsletterUpdateForm: React.FC<OfferNewsletterUpdateFormProps> = ({ offer_no, onSubmit }) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<API.OfferNewsletter[]>([]);

  const actionRef = useRef();

  // Handle saving changes
  const handleSave = useCallback(async () => {
    try {
      // await onSave(dataSource);
      message.success("Newsletters updated successfully");
    } catch (error) {
      message.error("Failed to update newsletters");
      console.error("Error saving newsletters:", error);
    }
  }, []);

  // Handle adding new newsletter
  const handleAddNewsletter = () => {
    const newNewsletter: API.OfferNewsletter = {
      id: Date.now(), // Temporary ID for new items
    };

    setDataSource([...dataSource, newNewsletter]);
    setEditableRowKeys([...editableKeys, newNewsletter.id!]);

    return newNewsletter;
  };

  // Handle deleting newsletter
  const handleDeleteNewsletter = (record: API.OfferNewsletter) => {
    const newDataSource = dataSource.filter((item) => item.id !== record.id);
    setDataSource(newDataSource);
    setEditableRowKeys(editableKeys.filter((key) => key !== record.id));
    message.success("Newsletter deleted");
  };

  // Define columns for the ProEditableTable
  const columns: ProColumns[] = [
    {
      title: "Title",
      dataIndex: "newsletter_title",
      key: "newsletter_title",
      width: 200,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "Newsletter title is required",
          },
        ],
      },
    },
    {
      title: "Content",
      dataIndex: "newsletter_content",
      key: "newsletter_content",
      width: 300,
      valueType: "textarea",
      formItemProps: {
        rules: [
          {
            required: true,
            message: "Newsletter content is required",
          },
        ],
      },
    },

    {
      title: "URL",
      dataIndex: "newsletter_url",
      key: "newsletter_url",
      width: 200,
      valueType: "text",
      formItemProps: {
        rules: [
          {
            type: "url",
            message: "Please enter a valid URL",
          },
        ],
      },
    },

    {
      title: "Actions",
      valueType: "option",
      width: 100,
      render(dom, entity, index, action, schema) {
        return [
          <a
            key="edit"
            onClick={() => {
              // action?.startEditable?.(record.id!);
            }}
          >
            Edit
          </a>,
          <Popconfirm
            key="delete"
            title="Are you sure you want to delete this newsletter?"
            // onConfirm={() => handleDeleteNewsletter(record)}
            okText="Yes"
            cancelText="No"
          >
            <a style={{ color: "red" }}>Delete</a>
          </Popconfirm>,
        ];
      },
    },
  ];

  return (
    <div>
      <EditableProTable<API.OfferNewsletter>
        actionRef={actionRef}
        rowKey="id"
        headerTitle={false}
        maxLength={50}
        recordCreatorProps={{
          record(index, dataSource) {
            const newNewsletter: API.OfferNewsletter = {
              id: Date.now(), // Temporary ID for new items
            };

            return newNewsletter;
          },
        }}
        columns={columns}
        value={dataSource}
        onChange={setDataSource as any}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("Saving row:", rowKey, data);
            return true;
          },
          onDelete: async (key) => {
            const newDataSource = dataSource.filter((item) => item.id !== key);
            setDataSource(newDataSource);
            return true;
          },
          onChange: setEditableRowKeys,
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        scroll={{ x: 1000 }}
        size="small"
      />

      <Space size={16} style={{ marginLeft: "auto", width: "100%" }}>
        <Button
          type="default"
          onClick={() => {
            formRef.current?.resetFields();
          }}
        >
          Reset
        </Button>
        <Button
          type="primary"
          onClick={() => {
            const hide = message.loading("Saving blog data...", 0);
            updateOrCreateOfferBlogByOfferNo({ offer_no, ...formRef.current?.getFieldsValue() })
              .then((res) => {
                hide();
                message.success("Saved successfully.");
                if (onSubmit) onSubmit(res);
              })
              .catch(Util.error)
              .finally(() => {
                hide();
              });
          }}
        >
          Save
        </Button>
      </Space>
    </div>
  );
};
export default OfferNewsletterUpdateForm;
