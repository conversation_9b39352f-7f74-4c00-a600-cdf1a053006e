import { getOrgOfferCommentAll } from "@/services/app/Supplier/supplier-call";
import Util from "@/util";
import { ActionType, ProColumns, ProFormFieldSet, ProTable } from "@ant-design/pro-components";
import { Modal } from "antd";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import OfferBlogUpdateForm from "./partials-blog-newsletter/OfferBlogUpdateForm";
import OfferNewsletterUpdateForm from "./partials-blog-newsletter/OfferNewsletterUpdateForm";

type RowType = APIOrg.OfferComment;

export type OfferBlogAndNewsletterModalProps = {
  offer_no: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const OfferBlogAndNewsletterModal: React.FC<OfferBlogAndNewsletterModalProps> = (props) => {
  const { offer_no, modalVisible, handleModalVisible } = props;

  const [reloadTickBlog, setReloadTickBlog] = useState(0);
  const [reloadTickNewsletter, setReloadTickNewsletter] = useState(0);

  useEffect(() => {
    setReloadTickBlog((prev) => prev + 1);
    setReloadTickNewsletter((prev) => prev + 1);
  }, [modalVisible]);

  return (
    <Modal title={`Offer Blog / Newsletter`} width="1200px" open={modalVisible} onCancel={() => handleModalVisible(false)} footer={false}>
      <ProFormFieldSet label="Blog Details" type="group">
        <OfferBlogUpdateForm offer_no={offer_no} reloadTick={reloadTickBlog} />
      </ProFormFieldSet>

      <ProFormFieldSet label="Newsletter" type="group">
        <OfferNewsletterUpdateForm offer_no={offer_no} />
      </ProFormFieldSet>
    </Modal>
  );
};
export default OfferBlogAndNewsletterModal;
